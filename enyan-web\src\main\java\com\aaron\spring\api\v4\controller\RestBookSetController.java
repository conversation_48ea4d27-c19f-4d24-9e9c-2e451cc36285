package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.GeoIPRequired;
import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.*;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.common.OrderUtil;
import com.aaron.spring.controller.ShopController;
import com.aaron.spring.model.*;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.*;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2023/9/1
 * @Modified By:
 */
@Slf4j
@RestController("RestBookSetControllerV4")
@RequestMapping(path = {"/api/v4/bookSet","/front/v4/bookSet"})
public class RestBookSetController extends RestBaseController{
	@Resource
	private EnyanBookService enyanBookService;

	@Resource
	private EnyanBookBuyService enyanBookBuyService;

	@Resource
	private EnyanBookSetService enyanBookSetService;

	@Resource
	private EnyanBookListService enyanBookListService;

	@Resource
	private EnyanOrderService enyanOrderService;

	@Resource
	private LogService  logService;

	@Resource
	private AuthUserService authUserService;

	/**
	 * <p>获取书系信息</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestBookSet>
	 * @since : 2023/9/5
	 **/
	@LoginAnonymous
	@RequestMapping(value = "/bookSetById",method = RequestMethod.POST)
	public ExecuteResult<RestBookSet> bookSetInfoWithId(@RequestBody RestBookSet restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		ExecuteResult<RestBookSet> result = new ExecuteResult<>();
		if (null == restObj.getSetId()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanBookSet obj = enyanBookSetService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
		if (null == obj){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
		RestBookSet bookSet = new RestBookSet();
		bookSet.initFrom(obj,currencyType);
		bookSet.setContainsPaidBook(calculateContainsPaidBookForSet(restObj, obj));
		if (null != restObj.getNeedLoginInfo() && true == restObj.getNeedLoginInfo()){
			bookSet.initNeedLogin(request);
		}
		result.setResult(bookSet);
		return result;
	}

	/**
	 * <p>获取书单信息</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestBookSet>
	 * @since : 2023/9/5
	 **/
	@LoginAnonymous
	@RequestMapping(value = "/bookMenuById",method = RequestMethod.POST)
	public ExecuteResult<RestBookSet> bookMenuInfoWithId(@RequestBody RestBookSet restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		ExecuteResult<RestBookSet> result = new ExecuteResult<>();
		if (null == restObj.getSetId()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanBookList obj = enyanBookListService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
		if (null == obj){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
//		log.info("EnyanBookList found: setId={}, price={}, priceDiscount={}, discountValue={}",
//				  obj.getSetId(), obj.getPrice(), obj.getPriceDiscount(), obj.getDiscountValue());
		
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
		RestBookSet bookSet = new RestBookSet();
		bookSet.initFromBookList(obj,currencyType);
		bookSet.setContainsPaidBook(calculateContainsPaidBookForMenu(restObj, obj));
		
//		log.info("RestBookSet after init: price={}, priceDiscount={}, priceCurrency={}",
//				  bookSet.getPrice(), bookSet.getPriceDiscount(), bookSet.getPriceCurrency());
		if (null != restObj.getNeedLoginInfo() && true == restObj.getNeedLoginInfo()){
			bookSet.initNeedLogin(request);
		}
		result.setResult(bookSet);
		return result;
	}

	/**
	 * <p>根据书系获取书籍列表</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestCart>
	 * @since : 2023/9/4
	 **/
	@LoginAnonymous
	@GeoIPRequired
	@RequestMapping(value = "/bookListBySet",method = RequestMethod.POST)
	public PageResult<RestBook> bookListInSet(@RequestBody RestBookSet restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		PageResult<RestBook> pageResult = new PageResult<>();
		if (null == restObj.getPage() || null == restObj.getSetId() || restObj.getSetId() <= 0){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}
		EnyanBookSet bookSet = enyanBookSetService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
		if (null == bookSet ){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}

		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
		EnyanBook enyanBook = new EnyanBook();
		Page<EnyanBook> page = new Page();
		page.setCurrentPage(restObj.getPage());
		page.setPageSize(pageResult.getPageSize());
		enyanBook.setPage(page);
		enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);

		enyanBook.setSetId(restObj.getSetId());

        OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
        enyanBook.addOrder(orderObj);
		page = enyanBookService.queryRecords(enyanBook.getPage(),enyanBook);
		pageResult.setCurrentPage(page.getCurrentPage());
		pageResult.setTotalRecord(page.getTotalRecord());

		if (null != bookSet.getIsDiscountValid() && bookSet.getIsDiscountValid() == 1 && null != bookSet.getDiscountValue()){//折扣是否有效
			BigDecimal discount = new BigDecimal(bookSet.getDiscountValue());//有折扣数据
			Byte isSingleValid = Constant.BYTE_VALUE_1;
			for (EnyanBook book : page.getRecords()){
				book.setDiscountSingleIsValid(isSingleValid);
				book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

				book.setDiscountId(null);
				book.setDiscountIsValid(Constant.BYTE_VALUE_0);
			}
		}

		for (EnyanBook book : page.getRecords()){
			book.resetByArea(restObj.getArea());
			RestBook tmp = new RestBook();
			tmp.initFrom(book,currencyType);
			tmp.setRecommendedCaption(book.getRecommendedCaption());

			if (null == tmp.getPrice()){//如果没有设置折扣信息，则原价为折扣价
				tmp.setPrice(tmp.getPriceDiscount());
			}

			pageResult.getResult().add(tmp);
		}

		return pageResult;
	}

	/**
	 * <p>根据书单获取书籍列表</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestCart>
	 * @since : 2023/9/4
	 **/
	@LoginAnonymous
	@GeoIPRequired
	@RequestMapping(value = "/bookListByMenu",method = RequestMethod.POST)
	public PageResult<RestBook> bookListInList(@RequestBody RestBookSet restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);

		PageResult<RestBook> pageResult = new PageResult<>();
		if (null == restObj.getPage() || null == restObj.getSetId() || restObj.getSetId() <= 0){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}
		EnyanBookList bookSet = enyanBookListService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
		if (null == bookSet ){
			pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return pageResult;
		}

		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
		EnyanBook enyanBook = new EnyanBook();
		Page<EnyanBook> page = new Page();
		page.setCurrentPage(restObj.getPage());
		page.setPageSize(pageResult.getPageSize());
		enyanBook.setPage(page);
		enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);

		enyanBook.setSetId(restObj.getSetId());

        OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
        enyanBook.addOrder(orderObj);
		page = enyanBookListService.queryBookRecords(enyanBook.getPage(),enyanBook,bookSet);
		pageResult.setCurrentPage(page.getCurrentPage());
		pageResult.setTotalRecord(page.getTotalRecord());

		if (null != bookSet.getIsDiscountValid() && bookSet.getIsDiscountValid() == 1 && null != bookSet.getDiscountValue()){//折扣是否有效
			BigDecimal discount = new BigDecimal(bookSet.getDiscountValue());//有折扣数据
			Byte isSingleValid = Constant.BYTE_VALUE_1;
			for (EnyanBook book : page.getRecords()){
				book.setDiscountSingleIsValid(isSingleValid);
				book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

				book.setDiscountId(null);
				book.setDiscountIsValid(Constant.BYTE_VALUE_0);
			}
		}

		for (EnyanBook book : page.getRecords()){
			book.resetByArea(restObj.getArea());
			RestBook tmp = new RestBook();
			tmp.initFrom(book,currencyType);
			tmp.setRecommendedCaption(book.getRecommendedCaption());

			if (null == tmp.getPrice()){//如果没有设置折扣信息，则原价为折扣价
				tmp.setPrice(tmp.getPriceDiscount());
			}

			pageResult.getResult().add(tmp);
		}


		return pageResult;
	}

	/**
	 * <p>购买书系的书籍展示列表</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestCart>
	 * @since : 2023/9/4
	 **/
@LoginAnonymous
@GeoIPRequired
@RequestMapping(value = "/buyBookSetToBuy",method = RequestMethod.POST)
public ExecuteResult<RestCart> buyBookSetToBuyMethod(@RequestBody RestBookSet restObj, HttpServletRequest request){
	restObj.initHeaderValue(request);

	CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

	ExecuteResult<RestCart> result = new ExecuteResult<>();
	if (null == restObj.getSetId()){
		result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
		return result;
	}
	// 若依然无法获取邮箱，则提示重新登录
	if (StringUtils.isBlank(restObj.getEmail())){
		result.addErrorMessage(InterfaceContant.ApiErrorConfig.LOGIN_ERROR_CODE);
		return result;
	}

	EnyanBookSet bookSet = enyanBookSetService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
	if (null == bookSet ){
		result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
		return result;
	}

		EnyanBook searchObj = new EnyanBook();
		searchObj.setShelfStatus(Constant.BYTE_VALUE_1);
		searchObj.setSetId(restObj.getSetId());
		searchObj.setPage(new Page<>());
		searchObj.getPage().setPageSize(-1);

		List<EnyanBook> list = enyanBookService.findBooks(searchObj);
		List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restObj.getEmail());

		HashSet<Long> booksHaveBuy = new HashSet<>();
		for (EnyanBookBuy bookBuy:bookBuyList){
			booksHaveBuy.add(bookBuy.getBookId());
		}

		if (null != bookSet.getIsDiscountValid() && bookSet.getIsDiscountValid() == 1 && null != bookSet.getDiscountValue()){//折扣是否有效
			BigDecimal discount = new BigDecimal(bookSet.getDiscountValue());//有折扣数据
			Byte isSingleValid = Constant.BYTE_VALUE_1;
			for (EnyanBook book : list){
				book.setDiscountSingleIsValid(isSingleValid);
				book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

				book.setDiscountId(null);
				book.setDiscountIsValid(Constant.BYTE_VALUE_0);
			}
		}

		List<RestBook> buyList = new ArrayList<>();
		List<RestBook> notBuyList = new ArrayList<>();
		for (EnyanBook enyanBook:list){
			enyanBook.resetByArea(restObj.getArea());

			RestBook tmp = new RestBook();
			tmp.initFrom(enyanBook,currencyType);
			if (null == tmp.getPrice()){//如果没有设置折扣信息，则原价为折扣价
				tmp.setPrice(tmp.getPriceDiscount());
			}
			//tmp.setPriceDiscount(tmp.getPrice().multiply(discount));
			if(booksHaveBuy.contains(enyanBook.getBookId().longValue())){//需要使用long
				buyList.add(tmp);
			}else {
				notBuyList.add(tmp);
			}
		}

		RestCart restCart = new RestCart();
		restCart.setBuyList(buyList);
		restCart.setNotBuyList(notBuyList);
		result.setResult(restCart);
		return result;
	}

	/**
	 * <p>购买书单的书籍展示列表</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestCart>
	 * @since : 2023/9/4
	 **/
@LoginAnonymous
@GeoIPRequired
@RequestMapping(value = "/buyBookMenuToBuy",method = RequestMethod.POST)
public ExecuteResult<RestCart> buyBookListToBuyMethod(@RequestBody RestBookSet restObj, HttpServletRequest request){
	restObj.initHeaderValue(request);
	CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

	ExecuteResult<RestCart> result = new ExecuteResult<>();
	if (null == restObj.getSetId()){
		result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
		return result;
	}
	// 若依然无法获取邮箱，则提示重新登录
	if (StringUtils.isBlank(restObj.getEmail())){
		result.addErrorMessage(InterfaceContant.ApiErrorConfig.LOGIN_ERROR_CODE);
		return result;
	}

	EnyanBookList bookSet = enyanBookListService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
	if (null == bookSet || StringUtils.isBlank(bookSet.getBookIdText())){
		result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
		return result;
	}
		List<Long> idsLong = Arrays.stream(bookSet.getBookIdText().split(","))
				                     .map(Long::parseLong)
				                     .collect(Collectors.toList());
		List<EnyanBook> list = enyanBookService.findBookByIds(idsLong);
		List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restObj.getEmail());

		if (null != bookSet.getIsDiscountValid() && bookSet.getIsDiscountValid() == 1 && null != bookSet.getDiscountValue()){//折扣是否有效
			BigDecimal discount = new BigDecimal(bookSet.getDiscountValue());//有折扣数据
			Byte isSingleValid = Constant.BYTE_VALUE_1;
			for (EnyanBook book : list){
				book.setDiscountSingleIsValid(isSingleValid);
				book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

				book.setDiscountId(null);
				book.setDiscountIsValid(Constant.BYTE_VALUE_0);
			}
		}

		HashSet<Long> booksHaveBuy = new HashSet<>();
		for (EnyanBookBuy bookBuy:bookBuyList){
			booksHaveBuy.add(bookBuy.getBookId());
		}
		List<RestBook> buyList = new ArrayList<>();
		List<RestBook> notBuyList = new ArrayList<>();
		for (EnyanBook enyanBook:list){
			enyanBook.resetByArea(restObj.getArea());

			RestBook tmp = new RestBook();
			tmp.initFrom(enyanBook,currencyType);
			if (null == tmp.getPrice()){//如果没有设置折扣信息，则原价为折扣价
				tmp.setPrice(tmp.getPriceDiscount());
			}
			//tmp.setPriceDiscount(tmp.getPrice().multiply(discount));
			if(booksHaveBuy.contains(enyanBook.getBookId().longValue())){//需要使用long
				buyList.add(tmp);
			}else {
				notBuyList.add(tmp);
			}
		}

		RestCart restCart = new RestCart();
		restCart.setBuyList(buyList);
		restCart.setNotBuyList(notBuyList);
		result.setResult(restCart);
		return result;
	}

	/**
	 * <p>确定书系购买的订单</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestOrder<com.aaron.spring.api.v4.model.RestBook>>
	 * @since : 2023/10/24
	 **/
	@LoginAnonymous
	@RequestMapping(value = "/buyBookSet",method = RequestMethod.POST)
	public ExecuteResult<RestOrder<RestBook>> buyBookSetAction(@RequestBody RestBookSet restObj, HttpServletRequest request) throws Exception {
		log.debug("buyBookSet");
		restObj.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

		ExecuteResult<RestOrder<RestBook>> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getSetId()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanBookSet bookSet = enyanBookSetService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
		if (null == bookSet ){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}

		List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restObj.getEmail());

		HashSet<Long> booksHaveBuy = new HashSet<>();
		for (EnyanBookBuy bookBuy:bookBuyList){
			booksHaveBuy.add(bookBuy.getBookId());
		}

		List<EnyanBook> mustBuyList = new ArrayList<>();//需要购买
		//List<RestBook> notBuyList = new ArrayList<>();
		EnyanBook searchObj = new EnyanBook();
		searchObj.setShelfStatus(Constant.BYTE_VALUE_1);
		searchObj.setSetId(restObj.getSetId());
		searchObj.setPage(new Page<>());
		searchObj.getPage().setPageSize(-1);

		List<EnyanBook> list = enyanBookService.findBooks(searchObj);

		for (EnyanBook book:list){
			if (booksHaveBuy.contains(book.getBookId().longValue()) == false){
				mustBuyList.add(book);
			}
		}

		if (mustBuyList.isEmpty()){
			result.addErrorMessage(ReturnInfo.ERROR_BOOK_BUY_NONE);
			return result;
		}

		if (null != bookSet.getIsDiscountValid() && bookSet.getIsDiscountValid() == 1 && null != bookSet.getDiscountValue()){//折扣是否有效
			BigDecimal discount = new BigDecimal(bookSet.getDiscountValue());//有折扣数据
			Byte isSingleValid = Constant.BYTE_VALUE_1;
			for (EnyanBook book : list){
				book.setDiscountSingleIsValid(isSingleValid);
				book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

				book.setDiscountId(null);
				book.setDiscountIsValid(Constant.BYTE_VALUE_0);
			}
		}

		BookPricingSummary pricingSummary = evaluateBooksPricing(mustBuyList, restObj.getArea());
		if (pricingSummary.isHasInvalidDiscount()) {
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restObj.getLang());
		for (EnyanBook enyanBook : pricingSummary.getBooks()){
			ProductInfo productInfo = new ProductInfo(enyanBook);
			cartInfo.addProduct(productInfo,1);
		}

		OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo, bookSet.getSetName());
		titleInfo.setBookType(EBookConstant.BookType.EBOOK_SET);

		OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
		EnyanOrder order = new EnyanOrder();
		order.setOrderNum(OrderUtil.getOrderId());
		order.setOrderTitleInfo(titleInfo);
		order.setOrderDetailInfo(detailInfo);

		order.setIsValid(Constant.BYTE_VALUE_1);
		order.setIsPaid(Constant.BYTE_VALUE_0);
		order.setIsCounted(Constant.BYTE_VALUE_0);

		order.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SET_BUY);

		order.setUserId(-1L);
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
		order.setUserEmail(restObj.getEmail());
		order.setPurchasedAt(new Date());
		order.setExpiredAt(DateUtils.addHours(order.getPurchasedAt(), 2));

		order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
		order.setOrderTotal(detailInfo.getAmountHkd());
		order.setOrderFrom(EBookConstant.OrderFrom.APP);

		enyanOrderService.addRecord(order);

		if (order.getOrderTotal().doubleValue() > 0){

		}else {//免费书直接分拆订单
			OrderPayInfo orderPayInfo = new OrderPayInfo();
			orderPayInfo.addFree();
			order.setOrderPayInfo(orderPayInfo);
			order.setIsPaid(Constant.BYTE_VALUE_1);
			new Thread(new Runnable(){
				@Override
				public void run() {
					paySuccess(order,order.getOrderPayInfo(),restObj.getLang());
				}
			}
			).start();
			order.setIsPaid(Constant.BYTE_VALUE_1);
		}

		RestOrder restOrder = new RestOrder();
		restOrder.initFrom(order,currencyType);
		restOrder.setContainsPaidBook(pricingSummary.isHasPaidBook());
		restOrder.setOrderId(order.getOrderId());
		result.setResult(restOrder);

		return result;
	}

	/**
	 * <p>确定书单购买的订单</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<com.aaron.spring.api.v4.model.RestOrder<com.aaron.spring.api.v4.model.RestBook>>
	 * @since : 2023/10/24
	 **/
	@LoginAnonymous
	@RequestMapping(value = "/buyBookMenu",method = RequestMethod.POST)
	public ExecuteResult<RestOrder<RestBook>> buyBookMenuAction(@RequestBody RestBookSet restObj, HttpServletRequest request) throws Exception {
		log.debug("buyBookMenu");
		restObj.initHeaderValue(request);
		CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());

		ExecuteResult<RestOrder<RestBook>> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getEmail()) || null == restObj.getSetId()){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanBookList bookSet = enyanBookListService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
		if (null == bookSet || StringUtils.isBlank(bookSet.getBookIdText())){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		List<Long> idsLong = Arrays.stream(bookSet.getBookIdText().split(","))
				                     .map(Long::parseLong)
				                     .collect(Collectors.toList());

		List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(restObj.getEmail());

		HashSet<Long> booksHaveBuy = new HashSet<>();
		for (EnyanBookBuy bookBuy:bookBuyList){
			booksHaveBuy.add(bookBuy.getBookId());
		}
		HashSet<Long> notBuySet = new HashSet<>();
		for (Long bookId:idsLong){
			if (booksHaveBuy.contains(bookId.longValue()) == false){
				notBuySet.add(bookId);
			}
		}
		if (notBuySet.isEmpty()){
			result.addErrorMessage(ReturnInfo.ERROR_BOOK_BUY_NONE);
			return result;
		}

		List<EnyanBook> mustBuyList = enyanBookService.findBookByIds(notBuySet.stream().collect(Collectors.toList()));//全部购买

		if (null != bookSet.getIsDiscountValid() && bookSet.getIsDiscountValid() == 1 && null != bookSet.getDiscountValue()){//折扣是否有效
			BigDecimal discount = new BigDecimal(bookSet.getDiscountValue());//有折扣数据
			Byte isSingleValid = Constant.BYTE_VALUE_1;
			for (EnyanBook book : mustBuyList){
				book.setDiscountSingleIsValid(isSingleValid);
				book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

				book.setDiscountId(null);
				book.setDiscountIsValid(Constant.BYTE_VALUE_0);
			}
		}

		BookPricingSummary pricingSummary = evaluateBooksPricing(mustBuyList, restObj.getArea());
		if (pricingSummary.isHasInvalidDiscount()) {
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,restObj.getLang());
		for (EnyanBook enyanBook : pricingSummary.getBooks()){
			ProductInfo productInfo = new ProductInfo(enyanBook);
			cartInfo.addProduct(productInfo,1);
		}

		OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo, bookSet.getSetName());
		titleInfo.setBookType(EBookConstant.BookType.EBOOK_MENU);

		OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
		EnyanOrder order = new EnyanOrder();
		order.setOrderNum(OrderUtil.getOrderId());
		order.setOrderTitleInfo(titleInfo);
		order.setOrderDetailInfo(detailInfo);

		order.setIsValid(Constant.BYTE_VALUE_1);
		order.setIsPaid(Constant.BYTE_VALUE_0);
		order.setIsCounted(Constant.BYTE_VALUE_0);

		order.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SET_BUY);

		order.setUserId(-1L);
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
		order.setUserEmail(restObj.getEmail());
		order.setPurchasedAt(new Date());
		order.setExpiredAt(DateUtils.addHours(order.getPurchasedAt(), 2));

		order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
		order.setOrderTotal(detailInfo.getAmountHkd());
		order.setOrderFrom(EBookConstant.OrderFrom.APP);

		enyanOrderService.addRecord(order);

		if (order.getOrderTotal().doubleValue() > 0){

		}else {//免费书直接分拆订单
			OrderPayInfo orderPayInfo = new OrderPayInfo();
			orderPayInfo.addFree();
			order.setOrderPayInfo(orderPayInfo);
			order.setIsPaid(Constant.BYTE_VALUE_1);
			new Thread(new Runnable(){
				@Override
				public void run() {
					paySuccess(order,order.getOrderPayInfo(),restObj.getLang());
				}
			}
			).start();
			order.setIsPaid(Constant.BYTE_VALUE_1);
		}

		RestOrder restOrder = new RestOrder();
		restOrder.initFrom(order,currencyType);
		restOrder.setContainsPaidBook(pricingSummary.isHasPaidBook());
		restOrder.setOrderId(order.getOrderId());
		result.setResult(restOrder);

		return result;
	}

	protected void paySuccess(EnyanOrder order, OrderPayInfo orderPayInfo, String lang) {
		log.debug("paySuccess:");
//        if (StringUtils.isBlank(lang)){
//            lang = InterfaceContant.LocaleLang.SC;
//        }
		order.setOrderPayInfo(orderPayInfo);
		if (order.getOrderDetailInfo()==null && org.springframework.util.StringUtils.hasLength(order.getOrderDetail())){
			String orderDetailInfo = order.getOrderDetail();

			OrderDetailInfo detailInfo = JSON.parseObject(orderDetailInfo, OrderDetailInfo.class);
			order.setOrderDetailInfo(detailInfo);
		}

		log.debug("paySuccess updateRecord");

		enyanOrderService.saveOrderHasPay(order);

//        this.sendMailOfOrder(order,request);
		ShopController.sendMailOfOrder(order, lang, getMessageSource(), getEmailService(), authUserService);
		this.sendLogOfOrder(order);
	}
	protected void sendLogOfOrder(EnyanOrder order) {
		log.debug("sendLogOfOrder:");
		if (org.springframework.util.StringUtils.hasLength(order.getUserEmail()) == false){
			return;
		}
		logService.sendOrderLog(order);
	}

	/**
	 * <p>获取所有书系ID列表</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<java.util.List<java.lang.Long>>
	 * @since : 2024-12-29
	 **/
	@LoginAnonymous
	@RequestMapping(value = "/allBookSetIds",method = RequestMethod.POST)
	public ExecuteResult<List<Long>> getAllBookSetIds(@RequestBody RestBookSet restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		
		ExecuteResult<List<Long>> result = new ExecuteResult<>();
		try {
			List<Long> setIds = enyanBookSetService.findAllBookSetIds();
			result.setResult(setIds);
		} catch (Exception e) {
			log.error("获取所有书系ID失败", e);
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
		}
		return result;
	}

	/**
	 * <p>获取所有书单ID列表</p>
	 * @param restObj
	 * @param request
	 * @return com.aaron.util.ExecuteResult<java.util.List<java.lang.Long>>
	 * @since : 2024-12-29
	 **/
	@LoginAnonymous
	@RequestMapping(value = "/allBookListIds",method = RequestMethod.POST)
	public ExecuteResult<List<Long>> getAllBookListIds(@RequestBody RestBookSet restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		
		ExecuteResult<List<Long>> result = new ExecuteResult<>();
		try {
			List<Long> listIds = enyanBookListService.findAllBookListIds();
			result.setResult(listIds);
		} catch (Exception e) {
			log.error("获取所有书单ID失败", e);
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
		}
		return result;
	}
	private boolean calculateContainsPaidBookForSet(RestBookSet restObj, EnyanBookSet bookSet) {
		if (null == bookSet || null == bookSet.getSetId()) {
			return false;
		}
		EnyanBook searchObj = new EnyanBook();
		searchObj.setShelfStatus(Constant.BYTE_VALUE_1);
		searchObj.setSetId(bookSet.getSetId());
		Page<EnyanBook> page = new Page<>();
		page.setPageSize(-1);
		searchObj.setPage(page);

		List<EnyanBook> books = enyanBookService.findBooks(searchObj);
		return resolveContainsPaidBook(restObj.getArea(), books, bookSet.getIsDiscountValid(), bookSet.getDiscountValue());
	}

	private boolean calculateContainsPaidBookForMenu(RestBookSet restObj, EnyanBookList bookList) {
		if (null == bookList || StringUtils.isBlank(bookList.getBookIdText())) {
			return false;
		}
		List<Long> ids = Arrays.stream(bookList.getBookIdText().split(","))
				.filter(StringUtils::isNotBlank)
				.map(Long::parseLong)
				.collect(Collectors.toList());
		if (ids.isEmpty()) {
			return false;
		}
		List<EnyanBook> books = enyanBookService.findBookByIds(ids);
		return resolveContainsPaidBook(restObj.getArea(), books, bookList.getIsDiscountValid(), bookList.getDiscountValue());
	}

	private boolean resolveContainsPaidBook(String area, List<EnyanBook> books, Integer isDiscountValid, Integer discountValue) {
		if (books == null || books.isEmpty()) {
			return false;
		}
		if (isDiscountValid != null && isDiscountValid == 1 && discountValue != null) {
			BigDecimal discount = new BigDecimal(discountValue);
			Byte isSingleValid = Constant.BYTE_VALUE_1;
			for (EnyanBook book : books) {
				if (book == null || book.getPriceHkd() == null) {
					continue;
				}
				book.setDiscountSingleIsValid(isSingleValid);
				book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100, Constant.NUM_SCALE_2, RoundingMode.HALF_UP));
				book.setDiscountId(null);
				book.setDiscountIsValid(Constant.BYTE_VALUE_0);
			}
		}
		BookPricingSummary pricingSummary = evaluateBooksPricing(books, area);
		return pricingSummary.isHasPaidBook();
	}
}





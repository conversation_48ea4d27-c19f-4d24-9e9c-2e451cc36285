package com.aaron.spring.api;

import com.aaron.spring.common.Constant;
import com.aaron.spring.service.EmailService;
import org.springframework.context.MessageSource;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.LocaleContextResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.math.BigDecimal;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/15
 * @Modified By:
 */
public class RestBaseController {
    private static final String Tip_Error = "isSaveError";

    private static final String Tip_Success = "isSaveSuccess";

    public static final String Tip_Msg = "msg";

    public static final String Error_Msg = "errorMsg";

    public static final int SYNC_PAGE_SIZE = 5;

    public static final String ERROR_NAME = "error.txt";
    @Resource
    private MessageSource messageSource;

    @Resource
    protected LocaleContextResolver localeResolver;

    @Resource
    private EmailService emailService;

    public void setErrorMsg(ModelMap modelMap, String msg){
        modelMap.addAttribute(Tip_Error, true);
        modelMap.addAttribute(Tip_Msg, msg);
    }
    public void setSuccessMsg(ModelMap modelMap,String msg){
        modelMap.addAttribute(Tip_Success, true);
        modelMap.addAttribute(Tip_Msg, msg);
    }

    public String getMessage(String code, HttpServletRequest request){
        return messageSource.getMessage(code,null,localeResolver.resolveLocale(request));
    }

    public String getRestEmail(HttpServletRequest req){
        String email = (String) req.getAttribute(Constant.ACCESS_EMAIL);
        return email;
    }

    public MessageSource getMessageSource() {
        return messageSource;
    }

    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    public EmailService getEmailService() {
        return emailService;
    }

    public void setEmailService(EmailService emailService) {
        this.emailService = emailService;
    }

    /**
     * Return异常信息
     */
    public interface ReturnInfo {
        String ERROR_E1 = "E1";
        String ERROR_E2 = "E2";
        String ERROR_E3 = "E3";
        String ERROR_E4 = "E4";

        String SUCCESS_EMAIL_SEND = "success.email.send";
        String SUCCESS_QUERY = "success.query";

        String ERROR_NAME_PASSWD_INVALID = "error.login.invalid";
        String ERROR_PASSWD_INVALID = "error.passwd.invalid";//密码错误
        String ERROR_ACCOUNT_NOT_ACTIVE = "error.login.not.active";
        String ERROR_ACTIVE_DXCEED = "error.active.exceed";//"超出设备激活数"
        String ERROR_EMAIL_NOT_EXIST = "error.email.not.exist";
        String ERROR_EMAIL_TOOMUCH = "error.email.toomuch";
        String ERROR_PARAM_INVALID = "error.param.invalid";
        String ERROR_ORDER_INVALID = "error.order.invalid";
        String ERROR_ORDER_EXIST = "error.order.exist";
        String ERROR_EMAIL_EXIST = "error.email.exist";//
        String ERROR_DATA_NONE = "error.data.none";
        /**
         * 灵修计划已经存在！
         */
        String ERROR_PLAN_EXIST = "error.plan.exist";

        /**
         * 灵修计划必须先购买书籍！
         */
        String ERROR_SPIRIT_MUST_BUY = "error.spirit.must.buy";

        String ERROR_COUPON_LIMIT = "error.coupon.limit";//订单满{0}方可使用该优惠码
        String ERROR_COUPON_INVALID = "error.coupon.invalid";//优惠码不合法
        /**
         * 已经使用过
         */
        String ERROR_COUPON_ONLY = "error.coupon.only";//已经使用过
        /**
         * 优惠码已过期
         */
        String ERROR_COUPON_EXPIRED = "error.coupon.expired";//优惠码已过期
        String ERROR_COUPON_ONLY_TIMES = "error.coupon.only.times";//该优惠码仅限使用N次
        String ERROR_COUPON_OUT = "error.coupon.out";//很遗憾，该优惠码已被抢完

        String ERROR_BOOK_PRESALE = "error.book.presale";//书籍预售中
        String ERROR_BOOK_BUY_NONE = "error.book.buy.none";//无可购买书籍

        String ERROR_PAY_FAIL = "error.pay.fail";//支付失败
        String ERROR_CARD_INVALID = "error.card.invalid";//非法卡号
        String MSG_PAY_SUCCESS = "msg.pay.success";//支付成功

        /**
         * 兑换码错误，请重新输入
         */
        String ERROR_REDEEMCODE_INVALID = "error.redeemCode.invalid";//兑换码错误，请重新输入
        /**
         * 兑换码已经失效
         */
        String ERROR_REDEEMCODE_HAS_EXCHANGE = "error.redeemCode.has.exchange";//兑换码已经失效
        /**
         * 书籍已存在
         */
        String ERROR_REDEEMCODE_BOOK_EXIST = "error.redeemCode.book.exist";//书籍已存在
        /**
         * 赠送兑换码失败！
         */
        String ERROR_REDEEMCODE_SEND = "error.redeemCode.send";//赠送兑换码失败！
        /**
         * 该兑换码已过期
         */
        String ERROR_REDEEMCODE_EXPIRED = "error.redeemCode.expired";//该兑换码已过期
        /**
         * 套餐租赁已存在
         */
        String ERROR_RENT_ORDER_EXIST = "error.rent.order.exist";//套餐租赁已存在
        /**
         * 有待支付订单
         */
        String ERROR_RENT_ORDER_NOT_PAY = "error.rent.order.not.pay";//有待支付订单

        /**
         * 订阅已经到期
         */
        String ERROR_RENT_EXPIRED = "error.rent.expired";//订阅已经到期

        // 图书馆相关错误码
        /**
         * 图书馆ID不能为空
         */
        String ERROR_LIBRARY_ID_REQUIRED = "error.library.id.required";
        
        /**
         * 用户未加入该图书馆
         */
        String ERROR_USER_NOT_IN_LIBRARY = "error.user.not.in.library";
        
        /**
         * 图书馆不存在
         */
        String ERROR_LIBRARY_NOT_EXIST = "error.library.not.exist";
        
        /**
         * 书籍不存在
         */
        String ERROR_BOOK_NOT_EXIST = "error.book.not.exist";
        
        /**
         * 书籍已被借出，无可用库存
         */
        String ERROR_BOOK_NO_AVAILABLE = "error.book.no.available";
        
        /**
         * 用户已借阅此书
         */
        String ERROR_BOOK_ALREADY_BORROWED = "error.book.already.borrowed";
        
        /**
         * 用户已预约此书
         */
        String ERROR_BOOK_ALREADY_RESERVED = "error.book.already.reserved";
        
        /**
         * 借阅记录不存在
         */
        String ERROR_BORROW_NOT_EXIST = "error.borrow.not.exist";
        
        /**
         * 预约记录不存在
         */
        String ERROR_RESERVATION_NOT_EXIST = "error.reservation.not.exist";
        
        /**
         * 书籍已归还
         */
        String ERROR_BOOK_ALREADY_RETURNED = "error.book.already.returned";
        
        /**
         * 借阅数量已达上限
         */
        String ERROR_BORROW_LIMIT_EXCEEDED = "error.borrow.limit.exceeded";
        
        /**
         * 预约数量已达上限
         */
        String ERROR_RESERVATION_LIMIT_EXCEEDED = "error.reservation.limit.exceeded";
        
        /**
         * 书籍有可用库存，无需预约
         */
        String ERROR_BOOK_AVAILABLE_NO_RESERVATION = "error.book.available.no.reservation";
        
        /**
         * 预约已取消，无法操作
         */
        String ERROR_RESERVATION_CANCELLED = "error.reservation.cancelled";
        
        /**
         * 图书馆用户状态异常
         */
        String ERROR_LIBRARY_USER_STATUS_INVALID = "error.library.user.status.invalid";
        
        /**
         * 分组名称不能为空
         */
        String ERROR_GROUP_NAME_REQUIRED = "error.group.name.required";
        
        /**
         * 书籍ID列表不能为空
         */
        String ERROR_BOOK_IDS_REQUIRED = "error.book.ids.required";
        
        /**
         * 馆藏图书ID列表不能为空
         */
        String ERROR_LIB_BOOK_IDS_REQUIRED = "error.lib.book.ids.required";

        // 图书馆成功消息
        /**
         * 借书成功
         */
        String SUCCESS_BOOK_BORROWED = "success.book.borrowed";
        
        /**
         * 还书成功
         */
        String SUCCESS_BOOK_RETURNED = "success.book.returned";
        
        /**
         * 预约成功
         */
        String SUCCESS_BOOK_RESERVED = "success.book.reserved";
        
        /**
         * 取消预约成功
         */
        String SUCCESS_RESERVATION_CANCELLED = "success.reservation.cancelled";
        
        /**
         * 分组更新成功
         */
        String SUCCESS_GROUP_UPDATED = "success.group.updated";
    }
    protected BookPricingSummary evaluateBooksPricing(java.util.List<com.aaron.spring.model.EnyanBook> books, String area) {
        BookPricingSummary summary = new BookPricingSummary();
        if (books == null || books.isEmpty()) {
            summary.setBooks(books == null ? java.util.Collections.emptyList() : books);
            return summary;
        }
        for (com.aaron.spring.model.EnyanBook book : books) {
            if (book == null) {
                continue;
            }
            book.resetByArea(area);
            boolean discountValid = Constant.BYTE_VALUE_1.equals(book.getDiscountSingleIsValid());
            java.math.BigDecimal basePrice = book.getPriceHkd();
            java.math.BigDecimal discountPrice = discountValid ? book.getPriceHKDDiscount() : null;

            // 修复免费书籍折扣验证逻辑：
            // 只有当原价大于0且折扣价格无效（null或负数）时才标记为无效折扣
            // 免费书籍（原价为0或折扣价为0）不应被视为无效折扣
            if (discountValid && discountPrice != null && discountPrice.compareTo(java.math.BigDecimal.ZERO) < 0) {
                // 只有折扣价格为负数时才是真正的无效折扣
                summary.setHasInvalidDiscount(true);
            } else if (discountValid && discountPrice == null && basePrice != null && basePrice.compareTo(java.math.BigDecimal.ZERO) > 0) {
                // 只有当原价大于0但折扣价格为null时才是无效折扣
                summary.setHasInvalidDiscount(true);
            }

            java.math.BigDecimal effectivePrice = discountPrice != null ? discountPrice : basePrice;
            if (effectivePrice != null && effectivePrice.compareTo(java.math.BigDecimal.ZERO) > 0) {
                summary.setHasPaidBook(true);
            }
        }
        summary.setBooks(books);
        return summary;
    }
    protected static class BookPricingSummary {
        private java.util.List<com.aaron.spring.model.EnyanBook> books = java.util.Collections.emptyList();
        private boolean hasPaidBook;
        private boolean hasInvalidDiscount;

        public java.util.List<com.aaron.spring.model.EnyanBook> getBooks() {
            return books;
        }

        public void setBooks(java.util.List<com.aaron.spring.model.EnyanBook> books) {
            this.books = books;
        }

        public boolean isHasPaidBook() {
            return hasPaidBook;
        }

        public void setHasPaidBook(boolean hasPaidBook) {
            this.hasPaidBook = hasPaidBook;
        }

        public boolean isHasInvalidDiscount() {
            return hasInvalidDiscount;
        }

        public void setHasInvalidDiscount(boolean hasInvalidDiscount) {
            this.hasInvalidDiscount = hasInvalidDiscount;
        }
    }

}

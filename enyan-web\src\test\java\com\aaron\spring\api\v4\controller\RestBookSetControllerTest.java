package com.aaron.spring.api.v4.controller;

import com.aaron.spring.api.v4.model.RestBookSet;
import com.aaron.spring.api.v4.model.RestOrder;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookBuy;
import com.aaron.spring.model.EnyanBookSet;
import com.aaron.spring.model.EnyanBookList;
import com.aaron.spring.service.*;
import com.aaron.util.ExecuteResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class RestBookSetControllerTest {

    @Mock
    private EnyanBookService enyanBookService;
    @Mock
    private EnyanBookBuyService enyanBookBuyService;
    @Mock
    private EnyanBookSetService enyanBookSetService;
    @Mock
    private EnyanBookListService enyanBookListService;
    @Mock
    private EnyanOrderService enyanOrderService;
    @Mock
    private LogService logService;
    @Mock
    private AuthUserService authUserService;

    @InjectMocks
    private RestBookSetController restBookSetController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 创建AaronJsonConverter
        com.aaron.a4f.converter.AaronJsonConverter jsonConverter = new com.aaron.a4f.converter.AaronJsonConverter();
        jsonConverter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON));

        mockMvc = MockMvcBuilders.standaloneSetup(restBookSetController)
                .setMessageConverters(jsonConverter)
                .build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void buyBookSet_shouldSucceedWithFreeBooks() throws Exception {
        // 准备测试数据
        RestBookSet request = new RestBookSet();
        request.setSetId(1L);
        request.setEmail("<EMAIL>");
        request.setCurrency("HKD");
        request.setArea("HK");

        // Mock 书系信息
        EnyanBookSet bookSet = new EnyanBookSet();
        bookSet.setSetId(1L);
        bookSet.setSetName("免费书系");
        bookSet.setIsDiscountValid(0);
        ExecuteResult<EnyanBookSet> bookSetResult = new ExecuteResult<>();
        bookSetResult.setResult(bookSet);
        when(enyanBookSetService.queryRecordByPrimaryKey(1L)).thenReturn(bookSetResult);

        // Mock 用户已购买的书籍（空列表）
        when(enyanBookBuyService.findBookIDAndNameByEmail("<EMAIL>")).thenReturn(new ArrayList<>());

        // Mock 书系中的免费书籍
        List<EnyanBook> freeBooks = new ArrayList<>();
        EnyanBook freeBook1 = new EnyanBook();
        freeBook1.setBookId(101L);
        freeBook1.setPriceHkd(BigDecimal.ZERO); // 免费书籍
        freeBooks.add(freeBook1);

        EnyanBook freeBook2 = new EnyanBook();
        freeBook2.setBookId(102L);
        freeBook2.setPriceHkd(BigDecimal.ZERO); // 免费书籍
        freeBooks.add(freeBook2);

        when(enyanBookService.findBooks(any())).thenReturn(freeBooks);

        // 执行测试
        mockMvc.perform(post("/api/v4/bookSet/buyBookSet")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)))
                .andExpect(jsonPath("$.result", notNullValue()));

        // 验证订单服务被调用
        verify(enyanOrderService, times(1)).addRecord(any());
    }

    @Test
    public void buyBookMenu_shouldSucceedWithFreeBooks() throws Exception {
        // 准备测试数据
        RestBookSet request = new RestBookSet();
        request.setSetId(1L);
        request.setEmail("<EMAIL>");
        request.setCurrency("HKD");
        request.setArea("HK");

        // Mock 书单信息
        EnyanBookList bookList = new EnyanBookList();
        bookList.setSetId(1L);
        bookList.setSetName("免费书单");
        bookList.setBookIdText("101,102");
        bookList.setIsDiscountValid(0);
        ExecuteResult<EnyanBookList> bookListResult = new ExecuteResult<>();
        bookListResult.setResult(bookList);
        when(enyanBookListService.queryRecordByPrimaryKey(1L)).thenReturn(bookListResult);

        // Mock 用户已购买的书籍（空列表）
        when(enyanBookBuyService.findBookIDAndNameByEmail("<EMAIL>")).thenReturn(new ArrayList<>());

        // Mock 书单中的免费书籍
        List<EnyanBook> freeBooks = new ArrayList<>();
        EnyanBook freeBook1 = new EnyanBook();
        freeBook1.setBookId(101L);
        freeBook1.setPriceHkd(BigDecimal.ZERO); // 免费书籍
        freeBooks.add(freeBook1);

        EnyanBook freeBook2 = new EnyanBook();
        freeBook2.setBookId(102L);
        freeBook2.setPriceHkd(BigDecimal.ZERO); // 免费书籍
        freeBooks.add(freeBook2);

        when(enyanBookService.findBookByIds(Arrays.asList(101L, 102L))).thenReturn(freeBooks);

        // 执行测试
        mockMvc.perform(post("/api/v4/bookSet/buyBookMenu")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)))
                .andExpect(jsonPath("$.result", notNullValue()));

        // 验证订单服务被调用
        verify(enyanOrderService, times(1)).addRecord(any());
    }

    @Test
    public void buyBookSet_shouldReturnErrorWhenAllBooksOwned() throws Exception {
        // 准备测试数据
        RestBookSet request = new RestBookSet();
        request.setSetId(1L);
        request.setEmail("<EMAIL>");
        request.setCurrency("HKD");
        request.setArea("HK");

        // Mock 书系信息
        EnyanBookSet bookSet = new EnyanBookSet();
        bookSet.setSetId(1L);
        bookSet.setSetName("测试书系");
        ExecuteResult<EnyanBookSet> bookSetResult = new ExecuteResult<>();
        bookSetResult.setResult(bookSet);
        when(enyanBookSetService.queryRecordByPrimaryKey(1L)).thenReturn(bookSetResult);

        // Mock 用户已购买的书籍
        List<EnyanBookBuy> ownedBooks = new ArrayList<>();
        EnyanBookBuy book1 = new EnyanBookBuy();
        book1.setBookId(101L);
        ownedBooks.add(book1);
        EnyanBookBuy book2 = new EnyanBookBuy();
        book2.setBookId(102L);
        ownedBooks.add(book2);
        when(enyanBookBuyService.findBookIDAndNameByEmail("<EMAIL>")).thenReturn(ownedBooks);

        // Mock 书系中的书籍（用户已全部拥有）
        List<EnyanBook> books = new ArrayList<>();
        EnyanBook book1Entity = new EnyanBook();
        book1Entity.setBookId(101L);
        books.add(book1Entity);
        EnyanBook book2Entity = new EnyanBook();
        book2Entity.setBookId(102L);
        books.add(book2Entity);
        when(enyanBookService.findBooks(any())).thenReturn(books);

        // 执行测试
        mockMvc.perform(post("/api/v4/bookSet/buyBookSet")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(false)))
                .andExpect(jsonPath("$.errorMessage", is("error.book.buy.none")));

        // 验证订单服务没有被调用
        verify(enyanOrderService, never()).addRecord(any());
    }
}

package com.aaron.spring.model;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 测试EnyanBook的resetByArea方法
 * 主要测试areaDiscount为null时不会抛出NullPointerException
 */
public class EnyanBookTest {

    @Test
    public void testResetByAreaWithNullAreaDiscount() {
        // 创建一个EnyanBook实例
        EnyanBook book = new EnyanBook();
        
        // 确保areaDiscount为null（默认值）
        assertNull("areaDiscount应该为null", book.getAreaDiscount());
        
        // 调用resetByArea方法，不应该抛出NullPointerException
        try {
            book.resetByArea("CN");
            // 如果没有抛出异常，测试通过
            assertTrue("resetByArea方法成功执行，没有抛出NullPointerException", true);
        } catch (NullPointerException e) {
            fail("resetByArea方法不应该抛出NullPointerException: " + e.getMessage());
        }
    }

    @Test
    public void testResetByAreaWithValidAreaDiscount() {
        // 创建一个EnyanBook实例
        EnyanBook book = new EnyanBook();
        
        // 设置areaDiscount为1（大陆免费）
        book.setAreaDiscount(1);
        
        // 调用resetByArea方法
        try {
            book.resetByArea("CN");
            // 验证方法正常执行
            assertTrue("resetByArea方法成功执行", true);
        } catch (Exception e) {
            fail("resetByArea方法不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testResetByAreaWithZeroAreaDiscount() {
        // 创建一个EnyanBook实例
        EnyanBook book = new EnyanBook();
        
        // 设置areaDiscount为0
        book.setAreaDiscount(0);
        
        // 调用resetByArea方法
        try {
            book.resetByArea("CN");
            // 验证方法正常执行
            assertTrue("resetByArea方法成功执行", true);
        } catch (Exception e) {
            fail("resetByArea方法不应该抛出异常: " + e.getMessage());
        }
    }
}
